{"rustc": 1842507548689473721, "features": "[\"32-column-tables\", \"default\", \"sqlite\", \"with-deprecated\"]", "declared_features": "[\"128-column-tables\", \"32-column-tables\", \"64-column-tables\", \"chrono\", \"default\", \"mysql\", \"nightly\", \"postgres\", \"r2d2\", \"sqlite\", \"time\", \"with-deprecated\", \"without-deprecated\"]", "target": 14327538309307208008, "profile": 2225463790103693989, "path": 13805140368588253795, "deps": [[3060637413840920116, "proc_macro2", false, 3290305129785117965], [4974441333307933176, "syn", false, 7581501629961639320], [9431064406736484046, "dsl_auto_type", false, 9818465038769094510], [12821317385041146439, "diesel_table_macro_syntax", false, 9944834954593333224], [17990358020177143287, "quote", false, 9901448601132589332]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\diesel_derives-048bf274bd70aaaa\\dep-lib-diesel_derives", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}