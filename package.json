{"private": true, "name": "rentable", "version": "0.1.0", "description": "a rents payment tracker", "type": "module", "scripts": {"dev": "vite dev", "build": "vite build", "preview": "vite preview", "prepare": "svelte-kit sync || echo ''", "check": "svelte-kit sync && svelte-check --tsconfig ./tsconfig.json && prettier --check .", "format": "prettier --write .", "lint": "prettier --check . && eslint .", "db:generate": "diesel migration generate --migration-dir src-tauri/migrations --config-file src-tauri/diesel.toml", "db:migrate": "diesel migration run --migration-dir src-tauri/migrations --config-file src-tauri/diesel.toml", "db:reset": "diesel database reset --migration-dir src-tauri/migrations --config-file src-tauri/diesel.toml", "tauri": "tauri"}, "dependencies": {"@tauri-apps/api": "^2", "@tauri-apps/plugin-opener": "^2"}, "devDependencies": {"@changesets/changelog-github": "^0.5.1", "@changesets/cli": "^2.29.5", "@eslint/compat": "^1.2.5", "@eslint/js": "^9.18.0", "@fontsource/fira-mono": "^5.0.0", "@internationalized/date": "^3.8.2", "@lucide/svelte": "^0.536.0", "@neoconfetti/svelte": "^2.0.0", "@sveltejs/adapter-static": "^3.0.8", "@sveltejs/kit": "^2.22.0", "@sveltejs/vite-plugin-svelte": "^6.0.0", "@tailwindcss/forms": "^0.5.9", "@tailwindcss/typography": "^0.5.15", "@tailwindcss/vite": "^4.0.0", "@tanstack/table-core": "^8.21.3", "@tauri-apps/cli": "^2", "bits-ui": "^2.9.1", "clsx": "^2.1.1", "embla-carousel-svelte": "^8.6.0", "eslint": "^9.18.0", "eslint-config-prettier": "^10.0.1", "eslint-plugin-svelte": "^3.0.0", "formsnap": "^2.0.1", "globals": "^16.0.0", "layerchart": "2.0.0-next.27", "mode-watcher": "^1.1.0", "paneforge": "^1.0.2", "prettier": "^3.4.2", "prettier-plugin-svelte": "^3.3.3", "prettier-plugin-tailwindcss": "^0.6.11", "svelte": "^5.25.0", "svelte-check": "^4.0.0", "svelte-sonner": "^1.0.5", "sveltekit-superforms": "^2.27.1", "tailwind-merge": "^3.3.1", "tailwind-variants": "^2.1.0", "tailwindcss": "^4.0.0", "tw-animate-css": "^1.3.6", "typescript": "^5.0.0", "typescript-eslint": "^8.20.0", "vaul-svelte": "1.0.0-next.7", "vite": "^7.0.4"}, "pnpm": {"onlyBuiltDependencies": ["@tailwindcss/oxide", "esbuild"]}, "engines": {"node": "^22.0.0", "pnpm": ">=10.0.0"}, "packageManager": "pnpm@10.14.0"}