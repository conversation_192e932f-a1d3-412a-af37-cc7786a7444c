{"rustc": 1842507548689473721, "features": "[]", "declared_features": "[]", "target": 3026416467940459999, "profile": 8731458305071235362, "path": 4942398508502643691, "deps": [[1696706501999471550, "rentable_lib", false, 16772432908739518197], [1696706501999471550, "build_script_build", false, 3440685334116142476], [3405707034081185165, "dotenvy", false, 4346918533700863965], [5112015475639934173, "libsqlite3_sys", false, 292247745151045468], [9689903380558560274, "serde", false, 3443111804069845985], [12092653563678505622, "tauri", false, 14192674036391440550], [15392423963319736265, "diesel", false, 12630573023060570049], [16362055519698394275, "serde_json", false, 11794512144438862786], [16702348383442838006, "tauri_plugin_opener", false, 14980254159691795126]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\rentable-00ecab133ca6a735\\dep-bin-rentable", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}