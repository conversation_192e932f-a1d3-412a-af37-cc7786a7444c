name: '🏷️ labeler'

on:
  pull_request_target:
    branches: ['*']

jobs:
  file-based-labeler:
    runs-on: ubuntu-latest

    permissions:
      contents: read
      pull-requests: write

    steps:
      - uses: actions/labeler@v5
        with:
          repo-token: '${{ secrets.GITHUB_TOKEN }}'
          sync-labels: true
  pr-title-labeler:
    runs-on: ubuntu-latest

    permissions:
      contents: read
      pull-requests: write

    steps:
      - uses: mauroalderete/action-assign-labels@v1
        with:
          pull-request-number: ${{ github.event.pull_request.number }}
          github-token: ${{ secrets.GITHUB_TOKEN }}
          maintain-labels-not-matched: false
          apply-changes: true
          conventional-commits: |
            conventional-commits:
              - type: 'fix'
                nouns: ['fix']
                labels: ['🐞 type: bug']

              - type: 'feature'
                nouns: ['feat']
                labels: ['✨ type: enhancement']

              - type: 'documentation'
                nouns: ['docs']
                labels: ['📚 type: documentation']

              - type: 'refactoring'
                nouns: ['refactor']
                labels: ['✨ type: enhancement']

              - type: 'continuous_integration'
                nouns: ['ci']
                labels: ['⚙️ type: tools']

              - type: 'testing'
                nouns: ['test']
                labels: ['🧪 type: tests']

              - type: 'build'
                nouns: ['build']
                labels: ['🏗️ type: build']

              - type: 'chore'
                nouns: ['chore']
                labels: ['🧹 type: chore']

              - type: 'breaking_change'
                nouns: ['BREAKING CHANGE', '!']
                labels: ['💥 flag: breaking changes']
