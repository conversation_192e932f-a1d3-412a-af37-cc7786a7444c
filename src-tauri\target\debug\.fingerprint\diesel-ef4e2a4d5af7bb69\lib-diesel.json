{"rustc": 1842507548689473721, "features": "[\"32-column-tables\", \"default\", \"returning_clauses_for_sqlite_3_35\", \"sqlite\", \"with-deprecated\"]", "declared_features": "[\"128-column-tables\", \"32-column-tables\", \"64-column-tables\", \"__with_asan_tests\", \"chrono\", \"default\", \"extras\", \"huge-tables\", \"i-implement-a-third-party-backend-and-opt-into-breaking-changes\", \"ipnet-address\", \"large-tables\", \"mysql\", \"mysql_backend\", \"mysqlclient-src\", \"network-address\", \"numeric\", \"postgres\", \"postgres_backend\", \"pq-src\", \"quickcheck\", \"r2d2\", \"returning_clauses_for_sqlite_3_35\", \"serde_json\", \"sqlite\", \"time\", \"unstable\", \"uuid\", \"with-deprecated\", \"without-deprecated\"]", "target": 17967542459835189317, "profile": 15657897354478470176, "path": 4321327009072335183, "deps": [[5112015475639934173, "libsqlite3_sys", false, 292247745151045468], [17810152143764662619, "diesel_derives", false, 14300815064014837790]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\diesel-ef4e2a4d5af7bb69\\dep-lib-diesel", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}