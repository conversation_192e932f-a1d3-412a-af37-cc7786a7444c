name: '🔧 integration'

on:
  pull_request:
    types: ['opened', 'reopened', 'synchronize', 'edited']

env:
  GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}

jobs:
  integration:
    timeout-minutes: 30
    runs-on: ubuntu-latest

    steps:
      - name: '☁️ checkout repository'
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: '📝 lint pull-request title'
        uses: amannn/action-semantic-pull-request@v5

      - name: '🛠️ setup pnpm'
        uses: pnpm/action-setup@v4

      - name: '🔧 setup node'
        uses: actions/setup-node@v4
        with:
          node-version: 22
          cache: 'pnpm'

      - name: '🔼 update rust'
        run: rustup update ${{ matrix.toolchain }} && rustup default stable

      - name: '💿 cargo cache'
        uses: actions/cache@v4
        with:
          path: |
            ~/.cargo/bin
            ~/.cargo/registry/index/
            ~/.cargo/registry/cache/
            ~/.cargo/git/db/
            ./src-tauri/target
          key: ${{ runner.os }}-cargo-pr-${{ github.head_ref || github.ref_name }}-${{ hashFiles('**/Cargo.lock') }}
          restore-keys: |
            ${{ runner.os }}-cargo-pr-${{ github.head_ref || github.ref_name }}-
            ${{ runner.os }}-cargo-main-${{ hashFiles('**/Cargo.lock') }}
            ${{ runner.os }}-cargo-main-

      - name: '📅 calculate date for apt packages cache key'
        id: date
        run: echo "week=$(date +%Y-W%U)" >> $GITHUB_OUTPUT

      - name: '💾 apt packages cache'
        uses: actions/cache@v4
        with:
          path: ~/apt-cache
          key: ${{ runner.os }}-apt-${{ steps.date.outputs.week }}-${{ hashFiles('.github/workflows/integration.yml') }}
          restore-keys: |
            ${{ runner.os }}-apt-${{ steps.date.outputs.week }}-
            ${{ runner.os }}-apt-

      - name: '📦 install dependencies'
        run: |
          pnpm install --frozen-lockfile
          sudo mkdir -p ~/apt-cache
          sudo chown -R $USER:$USER ~/apt-cache
          sudo apt-get update
          sudo apt-get install -y -o Dir::Cache::Archives="$HOME/apt-cache" libgtk-3-dev libsoup-3.0-dev libjavascriptcoregtk-4.1-dev libwebkit2gtk-4.1-dev librsvg2-dev
          sudo chown -R $USER:$USER ~/apt-cache

      - name: '🔎 check'
        run: pnpm check

      - name: '📦 build'
        run: pnpm tauri build
