name: '🚀 release'

on:
  push:
    branches: ['main']

concurrency: ${{ github.workflow }}-${{ github.ref }}

env:
  GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}

jobs:
  release:
    timeout-minutes: 30
    runs-on: ubuntu-latest

    steps:
      - name: '☁️ checkout repository'
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: '🛠️ setup pnpm'
        uses: pnpm/action-setup@v4

      - name: '🔧 setup node'
        uses: actions/setup-node@v4
        with:
          node-version: 22
          cache: 'pnpm'

      - name: '🔼 update rust'
        run: rustup update ${{ matrix.toolchain }} && rustup default stable

      - name: '💿 restore/save cargo cache (main)'
        uses: actions/cache@v4
        with:
          path: |
            ~/.cargo/bin
            ~/.cargo/registry/index/
            ~/.cargo/registry/cache/
            ~/.cargo/git/db/
            ./src-tauri/target
          key: ${{ runner.os }}-cargo-main-${{ hashFiles('**/Cargo.lock') }}
          restore-keys: |
            ${{ runner.os }}-cargo-main-

      - name: '📦 install dependencies'
        run: |
          pnpm install --frozen-lockfile 
          sudo apt-get update  
          sudo apt-get install -y libgtk-3-dev libsoup-3.0-dev libjavascriptcoregtk-4.1-dev libwebkit2gtk-4.1-dev librsvg2-dev

      - name: '🔎 check'
        run: pnpm check

      - name: '📦 build'
        run: pnpm tauri build

      - name: '🚀 release'
        uses: changesets/action@v1
        with:
          title: 'chore: version package'
          commit: 'chore: version package'
          version: node .github/changeset-version.cjs
