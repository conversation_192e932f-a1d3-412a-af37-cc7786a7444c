{"rustc": 1842507548689473721, "features": "", "declared_features": "", "target": 0, "profile": 0, "path": 0, "deps": [[5112015475639934173, "build_script_build", false, 18305788071382723574], [1696706501999471550, "build_script_build", false, 7245152440277249258], [12092653563678505622, "build_script_build", false, 7752102659210218402], [16702348383442838006, "build_script_build", false, 16185504798403696434]], "local": [{"RerunIfChanged": {"output": "debug\\build\\rentable-8f6c7ec7e91eecc6\\output", "paths": ["tauri.conf.json", "capabilities"]}}, {"RerunIfEnvChanged": {"var": "TAURI_CONFIG", "val": null}}, {"RerunIfEnvChanged": {"var": "REMOVE_UNUSED_COMMANDS", "val": null}}], "rustflags": [], "config": 0, "compile_kind": 0}