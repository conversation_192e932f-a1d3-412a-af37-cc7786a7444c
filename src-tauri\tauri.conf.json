{"$schema": "https://schema.tauri.app/config/2", "productName": "rentable", "version": "0.1.0", "identifier": "com.rentable.app", "build": {"beforeDevCommand": "pnpm dev", "devUrl": "http://localhost:1420", "beforeBuildCommand": "pnpm build", "frontendDist": "../build"}, "app": {"windows": [{"title": "rentable", "width": 800, "height": 600, "visible": false}], "security": {"csp": null}}, "bundle": {"active": true, "targets": "all", "icon": ["icons/32x32.png", "icons/128x128.png", "icons/<EMAIL>", "icons/icon.icns", "icons/icon.ico"]}}