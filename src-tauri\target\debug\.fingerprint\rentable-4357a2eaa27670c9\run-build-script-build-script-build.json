{"rustc": 1842507548689473721, "features": "", "declared_features": "", "target": 0, "profile": 0, "path": 0, "deps": [[5112015475639934173, "build_script_build", false, 18305788071382723574], [1696706501999471550, "build_script_build", false, 14557035842600030671], [12092653563678505622, "build_script_build", false, 14500403737866615248], [16702348383442838006, "build_script_build", false, 3297391100128747104]], "local": [{"RerunIfChanged": {"output": "debug\\build\\rentable-4357a2eaa27670c9\\output", "paths": ["tauri.conf.json", "capabilities"]}}, {"RerunIfEnvChanged": {"var": "TAURI_CONFIG", "val": null}}, {"RerunIfEnvChanged": {"var": "REMOVE_UNUSED_COMMANDS", "val": null}}], "rustflags": [], "config": 0, "compile_kind": 0}