{"rustc": 1842507548689473721, "features": "[]", "declared_features": "[]", "target": 2146541818774667668, "profile": 3316208278650011218, "path": 10763286916239946207, "deps": [[1696706501999471550, "build_script_build", false, 18175748570293814229], [3405707034081185165, "dotenvy", false, 14362014150923222639], [5112015475639934173, "libsqlite3_sys", false, 15987906678838718400], [9689903380558560274, "serde", false, 13969352957938392889], [12092653563678505622, "tauri", false, 14621005751261356712], [15392423963319736265, "diesel", false, 3402091018984612506], [16362055519698394275, "serde_json", false, 8655855931145446791], [16702348383442838006, "tauri_plugin_opener", false, 11477075053826845967]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\rentable-d129f8b7ef4fc879\\dep-test-lib-rentable_lib", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}