# credits
# https://github.com/trpc/trpc/blob/main/.github/ISSUE_TEMPLATE
# https://github.com/t3-oss/create-t3-app/tree/next/.github/ISSUE_TEMPLATE

name: 🧩 Feature Request
description: suggest an idea for this project
title: 'feat: '
labels: ['✨ type: enhancement', '📝 flag: triage']
body:
  - type: textarea
    id: problem
    attributes:
      label: Is your feature request related to a problem? Please describe.
      description: A clear and concise description of what the problem is. Ex. I'm always frustrated when [...]
    validations:
      required: true

  - type: textarea
    id: solution
    attributes:
      label: Describe the solution you'd like to see
      description: A clear and concise description of what you want to happen.
    validations:
      required: true

  - type: textarea
    id: alternative
    attributes:
      label: Describe alternate solutions
      description: A clear and concise description of any alternative solutions or features you've considered.
    validations:
      required: false

  - type: textarea
    id: other
    attributes:
      label: Additional information
      description: Add any other information related to the feature here. If your feature request is related to any issues or discussions, link them here.

  - type: checkboxes
    id: contribution
    attributes:
      label: 👨‍👧‍👦 Contributing
      description: Would you like to implement this feature?
      options:
        - label: 🙋‍♂️ Yes, I will create a pull request implementing this feature!
